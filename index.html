<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离心式风机启动实验</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- 背景图片容器 -->
        <div class="background-image"></div>
        
        <!-- 内容容器 -->
        <div class="content">
            <!-- 顶部logo和标题 -->
            <div class="header">
                <div class="logo">
                    <img src="./webgl/logo.png" alt="核电实验Logo" class="logo-img">
                </div>
                <h1 class="title">离心式风机启动实验</h1>
            </div>
            
            <!-- 开始实验按钮 -->
            <div class="button-container">
                <button class="start-button" onclick="startExperiment()">开始实验</button>
            </div>
        </div>

        <!-- 加载界面 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-content">
                <!-- 加载动画GIF -->
                <div class="loading-animation">
                    <img src="./webgl/loading.gif" alt="加载中..." class="loading-gif">
                </div>

                <!-- 加载进度条 -->
                <div class="loading-bar-container">
                    <div class="loading-bar-bg">
                        <img src="./webgl/loading_bar_bg.png" alt="进度条背景" class="loading-bar-bg-img">
                    </div>
                    <div class="loading-bar-progress" id="loadingBarProgress">
                        <img src="./webgl/loading_bar.png" alt="进度条" class="loading-bar-img">
                    </div>
                    <div class="loading-bar-top">
                        <img src="./webgl/loading_bar_top.png" alt="进度条顶部" class="loading-bar-top-img">
                    </div>
                </div>

                <!-- 加载文本 -->
                <div class="loading-text">
                    <p id="loadingText">正在加载实验环境...</p>
                    <p class="loading-percentage" id="loadingPercentage">0%</p>
                </div>
            </div>
        </div>

    </div>

    <script>
        function startExperiment() {
            // 显示加载界面
            const loadingOverlay = document.getElementById('loadingOverlay');
            loadingOverlay.style.display = 'flex';

            // 开始加载进度
            startLoading();
        }

        function startLoading() {
            const progressBar = document.getElementById('loadingBarProgress');
            const loadingText = document.getElementById('loadingText');
            const loadingPercentage = document.getElementById('loadingPercentage');

            let progress = 0;
            const loadingSteps = [
                { progress: 20, text: '初始化实验环境...' },
                { progress: 40, text: '加载风机模型...' },
                { progress: 60, text: '配置实验参数...' },
                { progress: 80, text: '准备实验界面...' },
                { progress: 100, text: '加载完成！' }
            ];

            let currentStep = 0;

            const loadingInterval = setInterval(() => {
                progress += 2;

                // 更新进度条宽度
                progressBar.style.width = progress + '%';
                loadingPercentage.textContent = progress + '%';

                // 更新加载文本
                if (currentStep < loadingSteps.length && progress >= loadingSteps[currentStep].progress) {
                    loadingText.textContent = loadingSteps[currentStep].text;
                    currentStep++;
                }

                // 加载完成
                if (progress >= 100) {
                    clearInterval(loadingInterval);
                    setTimeout(() => {
                        // 隐藏加载界面
                        document.getElementById('loadingOverlay').style.display = 'none';
                        // 这里可以跳转到实验页面
                        alert('实验环境加载完成！');
                        // window.location.href = 'experiment.html';
                    }, 1000);
                }
            }, 100); // 每100ms更新一次，总共5秒完成
        }
    </script>
</body>
</html>
