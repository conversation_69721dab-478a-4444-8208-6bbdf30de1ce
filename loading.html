<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在加载 - 离心式风机启动实验</title>
    <link rel="stylesheet" href="loading.css">
</head>
<body>
    <div class="loading-container">
        <!-- 背景图片 -->
        <div class="background-image"></div>
        
        <!-- 遮罩层 -->
        <div class="overlay"></div>
        
        <!-- 加载内容 -->
        <div class="loading-content">
            <!-- 左上角进度圆圈 -->
            <div class="progress-circle">
                <svg class="progress-ring" width="80" height="80">
                    <circle class="progress-ring-circle" 
                            stroke="#ffd700" 
                            stroke-width="4" 
                            fill="transparent" 
                            r="36" 
                            cx="40" 
                            cy="40"/>
                </svg>
                <div class="progress-text">
                    <span id="progress-percent">0%</span>
                </div>
            </div>
            
            <!-- 中心加载文字 -->
            <div class="loading-text">
                <h2>正在加载，请稍候......</h2>
                <div class="loading-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
                <p class="company-info">核电力科技有限公司</p>
            </div>
            
            <!-- 底部进度条 -->
            <div class="progress-bar-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-info">
                    <span>加载中...</span>
                    <span id="progress-status">0%</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let progress = 0;
        const progressPercent = document.getElementById('progress-percent');
        const progressStatus = document.getElementById('progress-status');
        const progressFill = document.getElementById('progress-fill');
        const progressCircle = document.querySelector('.progress-ring-circle');
        
        // 计算圆圈周长
        const radius = progressCircle.r.baseVal.value;
        const circumference = radius * 2 * Math.PI;
        
        progressCircle.style.strokeDasharray = `${circumference} ${circumference}`;
        progressCircle.style.strokeDashoffset = circumference;
        
        function updateProgress() {
            if (progress <= 100) {
                // 更新文字
                progressPercent.textContent = progress + '%';
                progressStatus.textContent = progress + '%';
                
                // 更新进度条
                progressFill.style.width = progress + '%';
                
                // 更新圆圈进度
                const offset = circumference - (progress / 100) * circumference;
                progressCircle.style.strokeDashoffset = offset;
                
                if (progress < 100) {
                    progress += Math.random() * 3 + 1; // 随机增加1-4%
                    if (progress > 100) progress = 100;
                    
                    setTimeout(updateProgress, 100 + Math.random() * 200);
                } else {
                    // 加载完成，跳转到实验页面
                    setTimeout(() => {
                        alert('加载完成！即将进入实验界面');
                        // window.location.href = 'experiment.html';
                        window.history.back(); // 暂时返回上一页
                    }, 500);
                }
            }
        }
        
        // 开始加载动画
        setTimeout(updateProgress, 1000);
    </script>
</body>
</html>
